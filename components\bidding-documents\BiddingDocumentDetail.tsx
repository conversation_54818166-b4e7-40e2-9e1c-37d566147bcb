'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  FileText, 
  Edit, 
  Download, 
  Upload, 
  FileCheck,
  AlertCircle,
  Plus,
  Trash2,
  Eye,
  Save,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Select } from '@/components/ui/Select'
import { TechnicalResponseEditor } from './TechnicalResponseEditor'
import { EvaluationSection } from './EvaluationSection'
import { EquipmentItemCard } from './EquipmentItemCard'
import { AddEquipmentDialog } from './AddEquipmentDialog'
import { BiddingDocumentUpload } from './BiddingDocumentUpload'
import { BiddingDocumentList } from './BiddingDocumentList'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import { 
  useGenerateTechnicalResponse,
  useExportTechnicalResponse,
  useUpdateBiddingDocument,
  useBiddingDocument,
  useDeleteAttachment
} from '@/hooks/queries/useBiddingDocuments'
import { useToast } from '@/hooks/useToast'
import type { BiddingDocument } from '@/types/biddingDocument'

interface BiddingDocumentDetailProps {
  document: BiddingDocument
}

export function BiddingDocumentDetail({ document: initialDocument }: BiddingDocumentDetailProps) {
  const router = useRouter()
  const toast = useToast()
  const [isAddEquipmentOpen, setIsAddEquipmentOpen] = useState(false)
  const [isEditingTechnicalResponse, setIsEditingTechnicalResponse] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [showUploadSection, setShowUploadSection] = useState(false)
  
  // Form state for editing
  const [formData, setFormData] = useState({
    name: initialDocument.name,
    description: initialDocument.description || '',
    customerName: initialDocument.customerName || '',
    status: initialDocument.status
  })
  
  // Use the hook to get real-time document data
  const { data: document = initialDocument } = useBiddingDocument(initialDocument.id)
  
  const generateMutation = useGenerateTechnicalResponse()
  const exportMutation = useExportTechnicalResponse()
  const updateMutation = useUpdateBiddingDocument()
  const deleteAttachmentMutation = useDeleteAttachment()

  const getStatusBadge = (status: BiddingDocumentStatus) => {
    const statusConfig = {
      [BiddingDocumentStatus.PENDING]: { label: 'Pending', className: 'bg-yellow-100 text-yellow-700' },
      [BiddingDocumentStatus.IN_PROGRESS]: { label: 'In Progress', className: 'bg-blue-100 text-blue-700' },
      [BiddingDocumentStatus.COMPLETED]: { label: 'Completed', className: 'bg-green-100 text-green-700' },
    }

    const config = statusConfig[status] || { label: status, className: 'bg-gray-100 text-gray-700' }
    
    return (
      <span className={`px-3 py-1 text-sm font-medium rounded-full ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const handleGenerateTechnicalResponse = () => {
    generateMutation.mutate({
      biddingDocumentId: document.id,
      format: 'WORD'
    })
  }

  const handleExport = (format: 'WORD' | 'PDF' | 'EXCEL') => {
    exportMutation.mutate({ id: document.id, format })
  }

  const canGenerateResponse = document.equipmentItems?.length > 0 && 
    document.equipmentItems.some(item => item.tenderDocuments?.length > 0)

  const canExport = document.status === BiddingDocumentStatus.COMPLETED

  const statusOptions = [
    { value: BiddingDocumentStatus.PENDING, label: 'Pending' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'In Progress' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Completed' },
  ]

  const handleSave = async () => {
    try {
      await updateMutation.mutateAsync({
        id: document.id,
        data: formData
      })
      setIsEditMode(false)
      toast.success('Cập nhật hồ sơ thành công')
    } catch (error) {
      console.error('Error updating document:', error)
      toast.error('Không thể cập nhật hồ sơ')
    }
  }

  const handleCancel = () => {
    setFormData({
      name: document.name,
      description: document.description || '',
      customerName: document.customerName || '',
      status: document.status
    })
    setIsEditMode(false)
  }

  return (
    <>
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-start mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Thông tin chung
            </h2>
            <div className="flex items-center gap-3">
              {!isEditMode && getStatusBadge(document.status)}
              {isEditMode ? (
                <>
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleSave}
                    disabled={updateMutation.isPending}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {updateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={updateMutation.isPending}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Hủy
                  </Button>
                </>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditMode(true)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Chỉnh sửa
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Mã hồ sơ</label>
              <p className="text-gray-900 dark:text-gray-100 font-medium">{document.code}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Trạng thái</label>
              {isEditMode ? (
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as BiddingDocumentStatus }))}
                  className="h-10"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              ) : (
                <div className="mt-1">
                  {getStatusBadge(document.status)}
                </div>
              )}
            </div>
            <div className="md:col-span-2">
              <label className="text-sm text-gray-500 dark:text-gray-400">Tên hồ sơ</label>
              {isEditMode ? (
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full h-10 px-3 py-2 mt-1 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                />
              ) : (
                <p className="text-gray-900 dark:text-gray-100">{document.name}</p>
              )}
            </div>
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Khách hàng</label>
              {isEditMode ? (
                <input
                  type="text"
                  value={formData.customerName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                  className="w-full h-10 px-3 py-2 mt-1 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Nhập tên khách hàng"
                />
              ) : (
                <p className="text-gray-900 dark:text-gray-100">{document.customerName || '-'}</p>
              )}
            </div>
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Ngày tạo</label>
              <p className="text-gray-900 dark:text-gray-100">
                {new Date(document.createdAt).toLocaleDateString('vi-VN')}
              </p>
            </div>
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Người tạo</label>
              <p className="text-gray-900 dark:text-gray-100">
                {document.createdBy.name || document.createdBy.username}
              </p>
            </div>
            <div>
              <label className="text-sm text-gray-500 dark:text-gray-400">Cập nhật lần cuối</label>
              <p className="text-gray-900 dark:text-gray-100">
                {new Date(document.updatedAt).toLocaleDateString('vi-VN')}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <label className="text-sm text-gray-500 dark:text-gray-400">Mô tả</label>
            {isEditMode ? (
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 mt-1 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="Nhập mô tả"
              />
            ) : (
              document.description && <p className="text-gray-900 dark:text-gray-100 mt-1">{document.description}</p>
            )}
          </div>
        </div>

        {/* Document Attachments Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Tài liệu đính kèm ({document.attachments?.length || 0})
            </h2>
            {isEditMode && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowUploadSection(!showUploadSection)}
              >
                <Upload className="w-4 h-4 mr-2" />
                {showUploadSection ? 'Ẩn tải lên' : 'Thêm tài liệu'}
              </Button>
            )}
          </div>

          {/* Upload Section */}
          {isEditMode && showUploadSection && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <BiddingDocumentUpload
                biddingDocumentId={document.id}
                onUploadComplete={() => {
                  toast.success('Tải lên tài liệu thành công')
                  setShowUploadSection(false)
                }}
              />
            </div>
          )}

          {/* Document List */}
          <BiddingDocumentList
            attachments={document.attachments || []}
            onDelete={async (attachmentId) => {
              // Handle delete without triggering profile update
              await deleteAttachmentMutation.mutateAsync({
                biddingDocumentId: document.id,
                attachmentId
              })
            }}
            canDelete={isEditMode}
            canView={true}
          />
        </div>

        {/* Equipment Items
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Danh sách thiết bị ({document.equipmentItems?.length || 0})
            </h2>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setIsAddEquipmentOpen(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Thêm thiết bị
            </Button>
          </div>

          {document.equipmentItems?.length > 0 ? (
            <div className="space-y-4">
              {document.equipmentItems.map((item) => (
                <EquipmentItemCard
                  key={item.id}
                  item={item}
                  biddingDocumentId={document.id}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">Chưa có thiết bị nào</p>
            </div>
          )}
        </div> */}

        {/* Technical Response Document */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Tài liệu đáp ứng kỹ thuật
            </h2>
            <div className="flex gap-2">
              {!document.technicalResponseDocument && canGenerateResponse && (
                <Button
                  type="button"
                  onClick={handleGenerateTechnicalResponse}
                  disabled={generateMutation.isPending}
                >
                  <FileCheck className="w-4 h-4 mr-2" />
                  {generateMutation.isPending ? 'Đang xử lý...' : 'Tạo tài liệu AI'}
                </Button>
              )}
              {canExport && (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleExport('WORD')}
                    disabled={exportMutation.isPending}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Word
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleExport('PDF')}
                    disabled={exportMutation.isPending}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    PDF
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleExport('EXCEL')}
                    disabled={exportMutation.isPending}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Excel
                  </Button>
                </>
              )}
            </div>
          </div>

          {document.technicalResponseDocument ? (
            <div className="space-y-4">
              <div className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <FileCheck className="w-8 h-8 text-green-500 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {document.technicalResponseDocument.fileName}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Được tạo lúc: {new Date(document.technicalResponseDocument.generatedAt).toLocaleString('vi-VN')}
                  </p>
                  {document.technicalResponseDocument.isEdited && (
                    <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                      Đã chỉnh sửa lúc: {new Date(document.technicalResponseDocument.lastEditedAt!).toLocaleString('vi-VN')}
                    </p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditingTechnicalResponse(true)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(document.technicalResponseDocument!.fileUrl, '_blank')}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* AI Processing Info */}
              {document.technicalResponseDocument.aiProcessedData && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-blue-800 dark:text-blue-300 font-medium">
                        Thông tin xử lý AI
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
                        Hệ thống AI đã phân tích và tạo tài liệu đáp ứng kỹ thuật dựa trên thông tin thiết bị và yêu cầu mời thầu.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">Chưa có tài liệu đáp ứng kỹ thuật</p>
              {!canGenerateResponse && (
                <p className="text-sm text-gray-400 mt-2">
                  Vui lòng thêm thiết bị và tải lên tài liệu mời thầu để tạo tài liệu
                </p>
              )}
            </div>
          )}
        </div>

        {/* Evaluation Section */}
        {document.technicalResponseDocument && (
          <EvaluationSection 
            document={document}
          />
        )}
      </div>

      {/* Dialogs */}
      <AddEquipmentDialog
        isOpen={isAddEquipmentOpen}
        onClose={() => setIsAddEquipmentOpen(false)}
        biddingDocumentId={document.id}
        existingEquipmentIds={document.equipmentItems?.map(item => item.equipmentId) || []}
      />

      {isEditingTechnicalResponse && document.technicalResponseDocument && (
        <TechnicalResponseEditor
          isOpen={true}
          onClose={() => setIsEditingTechnicalResponse(false)}
          document={document}
        />
      )}
    </>
  )
}