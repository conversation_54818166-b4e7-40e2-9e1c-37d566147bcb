import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userService, type UserFilters } from '@/services/userService'
import type { CreateUserInput, UpdateUserInput } from '@/services/userService'
import { toast } from 'sonner'

export function useUsers(filters: UserFilters = {}) {
  return useQuery({
    queryKey: ['users', filters],
    queryFn: ({ signal }) => userService.getUsers(filters, signal),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useUser(id: string) {
  return useQuery({
    queryKey: ['users', id],
    queryFn: ({ signal }) => userService.getUser(id, signal),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateUserInput) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Người dùng đã được tạo thành công')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Không thể tạo người dùng')
    },
  })
}

export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserInput }) =>
      userService.updateUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['users', variables.id] })
      toast.success('Người dùng đã được cập nhật')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Không thể cập nhật người dùng')
    },
  })
}

export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Người dùng đã được vô hiệu hóa')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Không thể vô hiệu hóa người dùng')
    },
  })
}

export function useCheckUsername() {
  return useMutation({
    mutationFn: (username: string) => userService.checkUsername(username),
  })
}

export function useCheckEmail() {
  return useMutation({
    mutationFn: (email: string) => userService.checkEmail(email),
  })
}

export function useResetPassword() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.resetPassword(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Mật khẩu đã được đặt lại thành: admin123')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Không thể đặt lại mật khẩu')
    },
  })
}

export function useToggleUserStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.toggleStatus(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success(data.message)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Không thể thay đổi trạng thái')
    },
  })
}