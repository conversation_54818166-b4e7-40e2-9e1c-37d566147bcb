'use client'

import React, { useState } from 'react'
import { Plus, Download, Upload, Users } from 'lucide-react'
import { MainLayout } from '@/components/layout/MainLayout'
import { UserTable } from '@/components/users/UserTable'
import { UserFilters } from '@/components/users/UserFilters'
import { UserFormDialog } from '@/components/users/UserFormDialog'
import { DeleteUserDialog } from '@/components/users/DeleteUserDialog'
import { Pagination } from '@/components/ui/Pagination'
import { Button } from '@/components/ui/Button'
import { useUsers } from '@/hooks/queries/useUsers'
import { useAuth } from '@/contexts/AuthContext'
import type { User } from '@/types/user'
import type { UserFilters as UserFiltersType } from '@/services/userService'
import { useRouter } from 'next/navigation'

export default function UsersPage() {
  const router = useRouter()
  const { user } = useAuth()
  
  const [filters, setFilters] = useState<UserFiltersType>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const [isFormOpen, setIsFormOpen] = useState(false)
  const [isDeleteOpen, setIsDeleteOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  const { data, isLoading } = useUsers(filters)

  React.useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      router.push('/dashboard')
    }
  }, [user, router])

  if (!user || user.role !== 'ADMIN') {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <p className="text-yellow-800 dark:text-yellow-200">
              Bạn không có quyền truy cập trang này.
            </p>
          </div>
        </div>
      </MainLayout>
    )
  }

  const handleFilterChange = (newFilters: UserFiltersType) => {
    setFilters(newFilters)
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handlePageSizeChange = (pageSize: number) => {
    setFilters({ ...filters, limit: pageSize, page: 1 })
  }

  const handleCreate = () => {
    setSelectedUser(null)
    setIsFormOpen(true)
  }

  const handleEdit = (user: User) => {
    setSelectedUser(user)
    setIsFormOpen(true)
  }

  const handleDelete = (targetUser: User) => {
    if (targetUser.id === user?.id) {
      return
    }
    setSelectedUser(targetUser)
    setIsDeleteOpen(true)
  }

  const handleExport = () => {
    console.log('Export users')
  }

  const handleImport = () => {
    console.log('Import users')
  }

  return (
    <MainLayout>
      <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                Quản lý người dùng
              </h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Quản lý tài khoản người dùng trong hệ thống
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="secondary"
                onClick={handleImport}
                className="flex items-center"
              >
                <Upload className="h-4 w-4 mr-2" />
                Nhập
              </Button>
              <Button
                variant="secondary"
                onClick={handleExport}
                className="flex items-center"
              >
                <Download className="h-4 w-4 mr-2" />
                Xuất
              </Button>
              <Button
                variant="primary"
                onClick={handleCreate}
                className="flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Thêm người dùng
              </Button>
            </div>
          </div>
        </div>

        {/* Stats */}
        {data && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Tổng người dùng</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {data.total}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                  <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Đang hoạt động</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {data.users.filter(u => u.status === 'ACTIVE').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                  <Users className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Quản trị viên</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {data.users.filter(u => u.role === 'ADMIN').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Email xác thực</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {data.users.filter(u => u.emailVerified).length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <UserFilters
            filters={filters}
            onFilterChange={handleFilterChange}
          />
        </div>

        {/* Table */}
        <div className="mb-6">
          <UserTable
            users={data?.users || []}
            isLoading={isLoading}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </div>

        {/* Pagination */}
        {data && data.totalPages > 1 && (
          <Pagination
            currentPage={data.page}
            totalPages={data.totalPages}
            totalRecords={data.total}
            pageSize={filters.limit || 10}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}

        {/* Dialogs */}
        <UserFormDialog
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          user={selectedUser}
        />

        <DeleteUserDialog
          isOpen={isDeleteOpen}
          onClose={() => setIsDeleteOpen(false)}
          user={selectedUser}
        />
      </div>
    </MainLayout>
  )
}