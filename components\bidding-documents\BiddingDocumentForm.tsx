'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCreateBiddingDocument, useUpdateBiddingDocument, useDeleteAttachment, useSaveAttachments, useCheckCodeExists } from '@/hooks/queries/useBiddingDocuments';
import { useEquipments } from '@/hooks/queries/useEquipment';
import { useToast } from '@/hooks/useToast';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { BiddingDocumentUpload } from './BiddingDocumentUpload';
import { BiddingDocumentList } from './BiddingDocumentList';
import { AIProfileCreationDialog } from './AIProfileCreationDialog';
import { FileText, Settings, Package, Plus, Trash2, AlertCircle, Upload, Sparkles } from 'lucide-react';
import { BiddingDocumentStatus } from '@/types/biddingDocument';
import type { BiddingDocument, CreateBiddingDocumentRequest, UpdateBiddingDocumentRequest, BiddingDocumentAttachment } from '@/types/biddingDocument';

interface BiddingDocumentFormProps {
  document?: BiddingDocument;
  onSuccess?: () => void;
}

interface EquipmentItem {
  equipmentId: string;
  pageRange?: {
    from: number;
    to: number;
  };
}

export function BiddingDocumentForm({ document, onSuccess }: BiddingDocumentFormProps) {
  const router = useRouter();
  const toast = useToast();
  const isEdit = !!document;

  const [formData, setFormData] = useState({
    code: document?.code || '',
    name: document?.name || '',
    description: document?.description || '',
    customerName: document?.customerName || '',
    status: document?.status || BiddingDocumentStatus.PENDING
  });

  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>(() => {
    if (document?.equipmentItems) {
      return document.equipmentItems.map(item => ({
        equipmentId: item.equipmentId,
        pageRange: item.pageRange
      }));
    }
    return [];
  });

  const [attachments, setAttachments] = useState<BiddingDocumentAttachment[]>(document?.attachments || []);
  const [isCheckingCode, setIsCheckingCode] = useState(false);
  const [codeError, setCodeError] = useState('');
  const [showAIDialog, setShowAIDialog] = useState(false);

  const { data: equipmentsData, isLoading: isLoadingEquipments } = useEquipments({ status: 'ACTIVE' });
  const equipments = equipmentsData?.data || [];

  const createMutation = useCreateBiddingDocument();
  const updateMutation = useUpdateBiddingDocument();
  const deleteAttachmentMutation = useDeleteAttachment();
  const saveAttachmentsMutation = useSaveAttachments();
  const checkCodeExistsMutation = useCheckCodeExists();

  useEffect(() => {
    if (document) {
      setFormData({
        code: document.code,
        name: document.name,
        description: document.description || '',
        customerName: document.customerName || '',
        status: document.status
      });
      if (document.equipmentItems) {
        setEquipmentItems(document.equipmentItems.map(item => ({
          equipmentId: item.equipmentId,
          pageRange: item.pageRange
        })));
      }
    }
  }, [document]);

  const handleCodeChange = async (code: string) => {
    setFormData(prev => ({ ...prev, code }));
    setCodeError('');
    
    if (code && !isEdit) {
      setIsCheckingCode(true);
      try {
        const exists = await checkCodeExistsMutation.mutateAsync(code);
        if (exists) {
          setCodeError('Mã hồ sơ đã tồn tại');
        }
      } catch (error) {
        console.error('Error checking code:', error);
      } finally {
        setIsCheckingCode(false);
      }
    }
  };

  const handleAddEquipmentItem = () => {
    setEquipmentItems([...equipmentItems, { equipmentId: '' }]);
  };

  const handleRemoveEquipmentItem = (index: number) => {
    setEquipmentItems(equipmentItems.filter((_, i) => i !== index));
  };

  const handleEquipmentItemChange = (index: number, field: keyof EquipmentItem, value: any) => {
    const newItems = [...equipmentItems];
    if (field === 'equipmentId') {
      newItems[index].equipmentId = value;
    } else if (field === 'pageRange' && value) {
      newItems[index].pageRange = value;
    }
    setEquipmentItems(newItems);
  };

  const handleUploadComplete = async (attachment: BiddingDocumentAttachment) => {
    try {
      const updatedAttachments = [...attachments, attachment];
      setAttachments(updatedAttachments);
    } catch (error) {
      console.error('Error handling upload:', error);
      toast.error('Lỗi khi xử lý file upload');
    }
  };

  const handleDeleteAttachment = async (attachmentId: string) => {
    try {
      // If it's an existing document (not temp), call API to delete
      if (!attachmentId.startsWith('temp-') && document?.id) {
        await deleteAttachmentMutation.mutateAsync({
          biddingDocumentId: document.id,
          attachmentId
        });
      }
      
      // Update local state
      const updatedAttachments = attachments.filter(att => att.id !== attachmentId);
      setAttachments(updatedAttachments);
    } catch (error) {
      console.error('Error deleting attachment:', error);
      toast.error('Lỗi khi xóa tài liệu');
    }
  };

  const handleAIProfileCreation = async (selectedFile: string, selectedEquipmentItems: EquipmentItem[]) => {
    // Update equipment items from dialog
    setEquipmentItems(selectedEquipmentItems);
    
    // TODO: Implement AI profile creation logic with selected file
    toast.success('Đã lưu thông tin để tạo hồ sơ bằng AI');
    
    setShowAIDialog(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("handleSubmit", e)

    if (!formData.code || !formData.name) {
      toast.error('Vui lòng nhập đầy đủ thông tin bắt buộc');
      return;
    }

    if (codeError) {
      toast.error('Vui lòng kiểm tra lại mã hồ sơ');
      return;
    }

    try {
      const validEquipmentItems = equipmentItems.filter(item => item.equipmentId);

      if (isEdit) {
        const updateData: UpdateBiddingDocumentRequest = {
          name: formData.name,
          description: formData.description,
          customerName: formData.customerName,
          status: formData.status,
          equipmentItems: validEquipmentItems
        };
        
        await updateMutation.mutateAsync({
          id: document.id,
          data: updateData
        });
        
        // Save new attachments if any were added
        const newAttachments = attachments.filter(att => att.id.startsWith('temp-'));
        if (newAttachments.length > 0) {
          try {
            await saveAttachmentsMutation.mutateAsync({
              biddingDocumentId: document.id,
              attachments: newAttachments
            });
            toast.success('Tài liệu mới đã được lưu thành công');
          } catch (error) {
            console.error('Error saving attachments:', error);
            toast.error('Lỗi khi lưu tài liệu. Vui lòng thử lại.');
          }
        }
        
        toast.success('Cập nhật hồ sơ dự thầu thành công');
      } else {
        const createData: CreateBiddingDocumentRequest = {
          code: formData.code,
          name: formData.name,
          description: formData.description,
          customerName: formData.customerName,
          status: formData.status,
          equipmentItems: validEquipmentItems
        };

        const newDocument = await createMutation.mutateAsync(createData);
        
        // Save attachments if any were selected
        if (newDocument && attachments.length > 0) {
          try {
            await saveAttachmentsMutation.mutateAsync({
              biddingDocumentId: newDocument.id,
              attachments: attachments
            });
            toast.success('Tài liệu đã được lưu thành công');
          } catch (error) {
            console.error('Error saving attachments:', error);
            toast.error('Lỗi khi lưu tài liệu. Vui lòng thử lại.');
          }
        }
        
        toast.success('Tạo hồ sơ dự thầu mới thành công');
      }

      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/bidding-documents');
      }
    } catch (error) {
      console.error('Failed to save bidding document:', error);
    }
  };

  const statusOptions = [
    { value: BiddingDocumentStatus.PENDING, label: 'Pending' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'In Progress' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Completed' }
  ];

  return (
    <form className="space-y-8">
      {/* Basic Information Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Thông tin cơ bản</h2>
              <p className="text-sm text-gray-600">Thông tin chính về hồ sơ dự thầu</p>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Mã hồ sơ <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.code}
                onChange={(e) => handleCodeChange(e.target.value)}
                disabled={isEdit}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
                placeholder="Nhập mã hồ sơ"
                required
              />
              {isCheckingCode && (
                <p className="mt-1 text-sm text-gray-500">Đang kiểm tra...</p>
              )}
              {codeError && (
                <p className="mt-1 text-sm text-red-600">{codeError}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Tên hồ sơ <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Nhập tên hồ sơ"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Khách hàng
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Nhập tên khách hàng"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Trạng thái
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as BiddingDocumentStatus })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>

            <div className="md:col-span-2 space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Mô tả
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white resize-none"
                rows={4}
                placeholder="Mô tả chi tiết về hồ sơ dự thầu..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* AI Profile Creation Button */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4">
          <div className="text-center">
            <Button
              type="button"
              variant="outline"
              size="lg"
              onClick={() => setShowAIDialog(true)}
              className="gap-3 px-8 py-4 bg-white border-2 border-purple-300 text-purple-600 hover:bg-purple-50 hover:border-purple-400 font-medium"
            >
              <Sparkles className="w-5 h-5" />
              Tạo hồ sơ bằng AI
            </Button>
            <p className="mt-3 text-sm text-gray-600">
              Sử dụng AI để tạo hồ sơ dự thầu từ tệp tin và danh sách thiết bị
            </p>
          </div>
        </div>
      </div>

      {/* Attachments Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg">
              <Upload className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Tài liệu đính kèm</h2>
              <p className="text-sm text-gray-600">
                {isEdit ? 'Quản lý tài liệu đính kèm' : 'Chọn tài liệu để tải lên sau khi tạo hồ sơ'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="space-y-6">
            <BiddingDocumentUpload
              biddingDocumentId={document?.id}
              onUploadComplete={handleUploadComplete}
              disabled={!isEdit}
            />
            
            {/* Instructions for create mode */}
            {!isEdit && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">Lưu ý:</p>
                    <p>Tài liệu sẽ được liên kết với hồ sơ sau khi tạo mới. File Word sẽ được upload lên Google Drive, file PDF sẽ được lưu trên server.</p>
                  </div>
                </div>
              </div>
            )}
            
            <BiddingDocumentList
              attachments={attachments}
              onDelete={handleDeleteAttachment}
              canDelete={isEdit}
              canView={isEdit}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-600">
            {isEdit ? 'Lưu thay đổi để cập nhật thông tin hồ sơ dự thầu' : 'Kiểm tra kỹ thông tin trước khi tạo hồ sơ mới'}
          </div>
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/bidding-documents')}
              className="px-6 py-3 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Hủy bỏ
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={createMutation.isPending || updateMutation.isPending || isCheckingCode}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {createMutation.isPending || updateMutation.isPending
                ? 'Đang xử lý...'
                : isEdit
                ? 'Cập nhật hồ sơ'
                : 'Tạo hồ sơ mới'}
            </Button>
          </div>
        </div>
      </div>
      <AIProfileCreationDialog
        isOpen={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onConfirm={handleAIProfileCreation}
        existingFiles={attachments.map(att => ({
          id: att.id,
          name: att.fileName,
          url: att.fileUrl
        }))}
        existingEquipmentItems={equipmentItems}
      />
    </form>
  );
}