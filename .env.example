# Database
DATABASE_URL="postgresql://user:password@localhost:5432/bidding_system?schema=public"

# Application
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# JWT Secrets
JWT_SECRET=your-secret-key-here
JWT_REFRESH_SECRET=your-refresh-secret-key-here

# Session Configuration
ACCESS_TOKEN_DURATION=10m
SESSION_DURATION=15m
REFRESH_DURATION=7d
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=15m

# Node Environment
NODE_ENV=development

# Google Drive Configuration
GOOGLE_DRIVE_FOLDER_ID=134k_Dqd8pwjXib1tJeOLrPUJGZSFWIYd
GOOGLE_REFRESH_TOKEN=1//0gEvycf38mnR4CgYIARAAGBASNwF-L9Ir_Mq4DjrK3r36naJXxvNOxlxHIXLBiIvtHMGmO2OhfvBC6ElZDESk4d5y6nG4J2j27jU

# Google OAuth2 Credentials (from oauth2_credentials.json)
GOOGLE_CLIENT_ID=121353485144-me2n7jbfuvrv7r1cmr4523midpqsmc8e.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3001/auth/callback