'use client';

import React, { useState, useEffect } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { useEquipments } from '@/hooks/queries/useEquipment';
import { useToast } from '@/hooks/useToast';
import { Equipment } from '@/types/equipment';
import { tokenManager } from '@/lib/tokenManager';
import { Trash2, Plus, AlertCircle, Download, FileSpreadsheet, Loader2, Sparkles } from 'lucide-react';

interface EquipmentItem {
  equipmentId: string;
  pageRange?: {
    from: number;
    to: number;
  };
}

interface AIProfileCreationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: (selectedFile: string, equipmentItems: EquipmentItem[]) => void;
  existingFiles?: { id: string; name: string; url: string }[];
  existingEquipmentItems?: EquipmentItem[];
}

interface AIGeneratedResult {
  excelFileUrl: string;
  excelFileName: string;
  gcsBucket?: string;
  gcsFilename?: string;
  downloadUrl: string;
}

export const AIProfileCreationDialog: React.FC<AIProfileCreationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  existingFiles = [],
  existingEquipmentItems = []
}) => {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [aiResult, setAiResult] = useState<AIGeneratedResult | null>(null);
  const [processing, setProcessing] = useState(false);
  const toast = useToast();

  // Fetch equipment from current inventory
  const { data: equipmentsData, isLoading: isLoadingEquipments, error: equipmentsError } = useEquipments({ status: 'ACTIVE' });
  const equipments = equipmentsData?.data || [];

  // Get all selected equipment IDs (current + existing)
  const getAllSelectedEquipmentIds = () => {
    const currentIds = equipmentItems.map(item => item.equipmentId).filter(id => id);
    const existingIds = existingEquipmentItems.map(item => item.equipmentId);
    return [...new Set([...currentIds, ...existingIds])];
  };

  // Check if equipment is already selected
  const isEquipmentAlreadySelected = (equipmentId: string) => {
    return getAllSelectedEquipmentIds().includes(equipmentId);
  };

  // Check if equipment is from existing items
  const isExistingEquipment = (equipmentId: string) => {
    return existingEquipmentItems.some(item => item.equipmentId === equipmentId);
  };
  
  // Debug logging
  useEffect(() => {
    if (equipmentsData) {
      console.log('Equipment data received:', equipmentsData);
    }
    if (equipmentsError) {
      console.error('Error fetching equipment:', equipmentsError);
    }
  }, [equipmentsData, equipmentsError]);

  useEffect(() => {
    if (!isOpen) {
      setSelectedFile('');
      setEquipmentItems([]);
      setAiResult(null);
      setProcessing(false);
    }
  }, [isOpen]);

  const handleAddEquipment = () => {
    setEquipmentItems([...equipmentItems, { equipmentId: '' }]);
  };

  const handleRemoveEquipment = (index: number) => {
    setEquipmentItems(equipmentItems.filter((_, i) => i !== index));
  };

  const handleEquipmentChange = (index: number, equipmentId: string) => {
    // Check if this equipment is already selected elsewhere
    if (equipmentId && isEquipmentAlreadySelected(equipmentId)) {
      // Check if it's the same item being changed
      const currentItemId = equipmentItems[index]?.equipmentId;
      if (currentItemId !== equipmentId) {
        if (isExistingEquipment(equipmentId)) {
          toast.error('Thiết bị này đã được đính kèm vào hồ sơ');
        } else {
          toast.error('Thiết bị này đã được chọn');
        }
        return;
      }
    }

    const newItems = [...equipmentItems];
    newItems[index] = { ...newItems[index], equipmentId };
    setEquipmentItems(newItems);
  };

  const handlePageRangeChange = (index: number, field: 'from' | 'to', value: string) => {
    const newItems = [...equipmentItems];
    const pageValue = parseInt(value) || 0;
    
    if (!newItems[index].pageRange) {
      newItems[index].pageRange = { from: 0, to: 0 };
    }
    
    newItems[index].pageRange![field] = pageValue;
    setEquipmentItems(newItems);
  };

  const handleGenerateAI = async () => {
    if (!selectedFile) {
      toast.error('Vui lòng chọn tệp tin');
      return;
    }

    if (equipmentItems.length === 0 || equipmentItems.every(item => !item.equipmentId)) {
      toast.error('Vui lòng chọn ít nhất một thiết bị');
      return;
    }

    setProcessing(true);
    setAiResult(null);

    try {
      // Get the access token
      const token = tokenManager.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Filter out empty equipment items
      const validEquipmentItems = equipmentItems.filter(item => item.equipmentId);

      // Prepare form data
      const formData = new FormData();
      formData.append('fileId', selectedFile);
      formData.append('equipmentItems', JSON.stringify(validEquipmentItems));

      // Call AI API
      const response = await fetch('/api/bidding-documents/ai-profile', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'AI processing failed');
      }

      const data = await response.json();
      
      if (data.success && data.result) {
        setAiResult(data.result);
        toast.success('Đã tạo hồ sơ thành công!');
      } else {
        throw new Error('Invalid response from AI');
      }
    } catch (error) {
      console.error('AI generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Lỗi khi tạo hồ sơ bằng AI');
    } finally {
      setProcessing(false);
    }
  };

  const handleDownload = () => {
    if (aiResult?.downloadUrl) {
      window.open(aiResult.downloadUrl, '_blank');
    }
  };

  const handleClose = () => {
    if (processing) {
      toast.warning('Đang xử lý, vui lòng chờ...');
      return;
    }
    
    // If AI was successful and onConfirm is provided, call it
    if (aiResult && onConfirm && selectedFile && equipmentItems.length > 0) {
      const validEquipmentItems = equipmentItems.filter(item => item.equipmentId);
      onConfirm(selectedFile, validEquipmentItems);
    }
    
    onClose();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={handleClose}
      title="Tạo hồ sơ bằng AI"
      description={aiResult ? "Hồ sơ đã được tạo thành công" : "Chọn tệp tin hiện có và danh sách thiết bị để tạo hồ sơ bằng AI"}
      className="max-w-3xl"
    >
      <div className="p-6">
        {aiResult ? (
          // Show AI Result
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileSpreadsheet className="w-12 h-12 text-green-600" />
                  <div>
                    <h3 className="text-lg font-semibold text-green-900">
                      Hồ sơ đã được tạo thành công!
                    </h3>
                    <p className="text-sm text-green-700 mt-1">
                      {aiResult.excelFileName}
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  onClick={handleDownload}
                  className="gap-2"
                >
                  <Download className="w-4 h-4" />
                  Tải xuống
                </Button>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Chi tiết file:</h4>
              <dl className="space-y-1 text-sm">
                <div className="flex">
                  <dt className="text-gray-500 w-32">Tên file:</dt>
                  <dd className="text-gray-900">{aiResult.excelFileName}</dd>
                </div>
                {aiResult.gcsBucket && (
                  <div className="flex">
                    <dt className="text-gray-500 w-32">Bucket:</dt>
                    <dd className="text-gray-900">{aiResult.gcsBucket}</dd>
                  </div>
                )}
                {aiResult.gcsFilename && (
                  <div className="flex">
                    <dt className="text-gray-500 w-32">Path:</dt>
                    <dd className="text-gray-900">{aiResult.gcsFilename}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>
        ) : (
          // Show Form
          <div className="space-y-6">
          {/* File Picker Section */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Chọn tệp tin PDF</label>
            <Select
              value={selectedFile}
              onChange={(e) => setSelectedFile(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Chọn tệp tin từ danh sách</option>
              {existingFiles.map((file) => (
                <option key={file.id} value={file.id}>
                  {file.name}
                </option>
              ))}
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              <AlertCircle className="w-3 h-3 inline mr-1" />
              Chỉ hỗ trợ file PDF được lưu trên server
            </p>
          </div>

          {/* Equipment Items Section */}
          <div className="space-y-2">
            <div className="flex justify-between items-center mb-2">
              <label className="text-sm font-medium">Danh sách thiết bị</label>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleAddEquipment}
                className="gap-2"
              >
                <Plus className="w-4 h-4" />
                Thêm thiết bị
              </Button>
            </div>

            {existingEquipmentItems.length > 0 && (
              <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-2">
                <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-900">
                    Hồ sơ này đã có {existingEquipmentItems.length} thiết bị được đính kèm
                  </p>
                  <p className="text-xs text-blue-700 mt-1">
                    Các thiết bị này không thể được chọn lại trong danh sách mới.
                  </p>
                </div>
              </div>
            )}

            {isLoadingEquipments ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-gray-500">Đang tải danh sách thiết bị...</p>
              </div>
            ) : equipmentsError ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-red-500">Lỗi khi tải danh sách thiết bị</p>
              </div>
            ) : equipmentItems.length === 0 ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-gray-500">
                  Chưa có thiết bị nào. Nhấn "Thêm thiết bị" để bắt đầu.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {equipmentItems.map((item, index) => (
                  <div key={index} className="flex gap-2 items-start border rounded-lg p-3">
                    <div className="flex-1">
                      <Select
                        value={item.equipmentId}
                        onChange={(e) => handleEquipmentChange(index, e.target.value)}
                        className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                        disabled={isLoadingEquipments}
                      >
                        <option value="">Chọn thiết bị</option>
                        {equipments.map((equipment) => {
                          const isSelected = isEquipmentAlreadySelected(equipment.id);
                          const isFromExisting = isExistingEquipment(equipment.id);
                          const isCurrentItem = item.equipmentId === equipment.id;
                          const isDisabled = isSelected && !isCurrentItem;
                          
                          return (
                            <option 
                              key={equipment.id} 
                              value={equipment.id}
                              disabled={isDisabled}
                            >
                              {equipment.name} - {equipment.equipmentCode}
                              {isFromExisting && !isCurrentItem && ' (Đã đính kèm)'}
                              {isSelected && !isFromExisting && !isCurrentItem && ' (Đã chọn)'}
                            </option>
                          );
                        })}
                      </Select>
                    </div>

                    <div className="flex gap-2 items-center">
                      <input
                        type="number"
                        placeholder="Từ trang"
                        className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        min="1"
                        value={item.pageRange?.from || ''}
                        onChange={(e) => handlePageRangeChange(index, 'from', e.target.value)}
                      />
                      <span className="text-sm">-</span>
                      <input
                        type="number"
                        placeholder="Đến trang"
                        className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        min="1"
                        value={item.pageRange?.to || ''}
                        onChange={(e) => handlePageRangeChange(index, 'to', e.target.value)}
                      />
                    </div>

                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveEquipment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        )}

        <div className="mt-6 flex justify-end gap-3">
          <Button 
            type="button"
            variant="outline" 
            onClick={handleClose} 
            disabled={processing}
          >
            {aiResult ? 'Đóng' : 'Hủy'}
          </Button>
          {!aiResult && (
            <Button 
              type="button"
              onClick={handleGenerateAI} 
              disabled={processing || !selectedFile || equipmentItems.length === 0}
              className="gap-2"
            >
              {processing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Đang xử lý...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4" />
                  Tạo hồ sơ với AI
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </Dialog>
  );
};