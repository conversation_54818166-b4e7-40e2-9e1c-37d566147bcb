import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import {
  verifyPassword,
  generateAccessToken,
  generateRefreshToken,
  generateSessionToken,
  getExpirationDate,
  getClientIp,
  getUserAgent,
  loginSchema,
  AuditAction,
  AUTH_COOKIE_OPTIONS,
  REFRESH_COOKIE_OPTIONS,
} from '@/lib/auth'

// Rate limiting in-memory store (in production, use Redis)
const loginAttempts = new Map<string, { count: number; lockedUntil?: Date }>()

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validation = loginSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid credentials format', details: validation.error.flatten() },
        { status: 400 }
      )
    }

    const { username, password } = validation.data
    const clientIp = getClientIp(request)
    const userAgent = getUserAgent(request)

    // Check rate limiting
    const attempts = loginAttempts.get(clientIp) || { count: 0 }
    if (attempts.lockedUntil && attempts.lockedUntil > new Date()) {
      // Log rate limiting attempt (without userId for unknown users)
      console.log('Login rate limited:', { username, ipAddress: clientIp })

      return NextResponse.json(
        { error: 'Too many login attempts. Please try again later.' },
        { status: 429 }
      )
    }

    // Find user by username
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        email: true,
        passwordHash: true,
        name: true,
        role: true,
        department: true,
        phone: true,
        avatar: true,
        status: true,
        emailVerified: true,
        twoFactorEnabled: true,
        loginAttempts: true,
        lockedUntil: true,
      },
    })

    if (!user) {
      // Track failed attempt
      attempts.count++
      
      // Lock IP after 5 failed attempts
      if (attempts.count >= 5) {
        attempts.lockedUntil = getExpirationDate('15m')
      }
      
      loginAttempts.set(clientIp, attempts)

      // Log failed attempt (without userId for non-existent users)
      console.log('Login failed - user not found:', { username, ipAddress: clientIp, attempts: attempts.count })

      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      )
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: AuditAction.LOGIN_FAILED,
          details: { reason: 'Account locked' },
          ipAddress: clientIp,
        },
      })

      return NextResponse.json(
        { error: 'Account is locked. Please contact support.' },
        { status: 403 }
      )
    }

    // Check if account is active
    if (user.status !== 'ACTIVE') {
      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: AuditAction.LOGIN_FAILED,
          details: { reason: 'Account inactive' },
          ipAddress: clientIp,
        },
      })

      return NextResponse.json(
        { error: 'Account is inactive. Please contact support.' },
        { status: 403 }
      )
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    
    if (!isValidPassword) {
      // Increment login attempts
      const newAttempts = user.loginAttempts + 1
      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5')
      
      const updateData: any = {
        loginAttempts: newAttempts,
      }

      // Lock account if max attempts exceeded
      if (newAttempts >= maxAttempts) {
        const lockoutDuration = process.env.LOCKOUT_DURATION || '15m'
        updateData.lockedUntil = getExpirationDate(lockoutDuration)
        
        await prisma.auditLog.create({
          data: {
            userId: user.id,
            action: AuditAction.ACCOUNT_LOCKED,
            details: { attempts: newAttempts },
            ipAddress: clientIp,
          },
        })
      }

      await prisma.user.update({
        where: { id: user.id },
        data: updateData,
      })

      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: AuditAction.LOGIN_FAILED,
          details: { reason: 'Invalid password', attempts: newAttempts },
          ipAddress: clientIp,
        },
      })

      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      )
    }

    // Create session
    const sessionToken = generateSessionToken()
    const sessionDuration = process.env.SESSION_DURATION || '15m'
    const refreshDuration = process.env.REFRESH_DURATION || '7d'
    
    // Generate tokens first
    const tempSessionId = generateSessionToken() // temporary ID for token generation
    
    const accessToken = await generateAccessToken({
      userId: user.id,
      username: user.username,
      role: user.role,
      sessionId: tempSessionId,
    })

    const refreshToken = await generateRefreshToken({
      userId: user.id,
      sessionId: tempSessionId,
    })
    
    // Create session with the actual refresh token
    const session = await prisma.session.create({
      data: {
        id: tempSessionId,
        userId: user.id,
        token: sessionToken,
        refreshToken: refreshToken, // Store the actual refresh token
        ipAddress: clientIp,
        userAgent,
        expiresAt: getExpirationDate(sessionDuration),
      },
    })

    // Reset login attempts and update last login
    await prisma.user.update({
      where: { id: user.id },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date(),
      },
    })

    // Clear rate limiting for this IP
    loginAttempts.delete(clientIp)

    // Log successful login
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: AuditAction.LOGIN_SUCCESS,
        details: { sessionId: session.id },
        ipAddress: clientIp,
      },
    })

    // Prepare user response (without sensitive data)
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      department: user.department,
      phone: user.phone,
      avatar: user.avatar,
      status: user.status,
      emailVerified: user.emailVerified,
      twoFactorEnabled: user.twoFactorEnabled,
    }

    // Set only refresh token as HttpOnly cookie
    const cookieStore = await cookies()
    cookieStore.set('refresh-token', refreshToken, REFRESH_COOKIE_OPTIONS)
    cookieStore.set('session-id', session.id, REFRESH_COOKIE_OPTIONS)

    // Return only access token in response body (not in cookies)
    return NextResponse.json({
      user: userResponse,
      accessToken,
      expiresIn: 600, // 10 minutes in seconds
    })
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred during login' },
      { status: 500 }
    )
  }
}