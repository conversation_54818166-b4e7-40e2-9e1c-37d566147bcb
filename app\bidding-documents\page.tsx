'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/MainLayout'
import { BiddingDocumentTable } from '@/components/bidding-documents/BiddingDocumentTable'
import { BiddingDocumentFilters } from '@/components/bidding-documents/BiddingDocumentFilters'
import { Button } from '@/components/ui/Button'
import { Plus } from 'lucide-react'
import { useDebounce } from '@/hooks/useDebounce'
import { useBiddingDocuments } from '@/hooks/queries/useBiddingDocuments'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import type { BiddingDocumentFilter } from '@/types/biddingDocument'

export default function BiddingDocumentsPage() {
  const router = useRouter()
  const [filters, setFilters] = useState<BiddingDocumentFilter>({
    page: 1,
    limit: 10,
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilterNotice, setShowFilterNotice] = useState(false)
  
  const debouncedSearch = useDebounce(searchTerm, 500)
  
  const { data, isLoading, refetch, isError, error } = useBiddingDocuments({
    ...filters,
    search: debouncedSearch,
  })

  const handleFilterChange = (newFilters: Partial<BiddingDocumentFilter>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }))
    setShowFilterNotice(false)
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setShowFilterNotice(false)
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }
  
  const handlePageSizeChange = (limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }))
  }

  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Quản lý hồ sơ dự thầu
            </h1>
            <Button
              onClick={() => router.push('/bidding-documents/new')}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Thêm mới
            </Button>
          </div>
          
          {showFilterNotice && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                Bộ lọc đã được đặt lại để hiển thị hồ sơ mới tạo.
              </p>
            </div>
          )}
          
          <BiddingDocumentFilters
            onFilterChange={handleFilterChange}
            onSearch={handleSearch}
            searchTerm={searchTerm}
            filters={filters}
          />
        </div>

        {isError && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  Không thể tải danh sách hồ sơ
                </p>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error instanceof Error ? error.message : 'Đã xảy ra lỗi không xác định'}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="text-red-700 dark:text-red-300 border-red-300 dark:border-red-700"
              >
                Thử lại
              </Button>
            </div>
          </div>
        )}

        <BiddingDocumentTable
          documents={data?.items || []}
          isLoading={isLoading}
          pagination={{
            page: data?.page || 1,
            limit: data?.limit || 10,
            total: data?.total || 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
          }}
          onCreateClick={() => router.push('/bidding-documents/new')}
          hasActiveFilters={!!searchTerm || !!filters.status || !!filters.customerName}
        />
      </div>
    </MainLayout>
  )
}