'use client'

import React, { useEffect, useState } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { But<PERSON> } from '@/components/ui/Button'
import { useCreateCatalog, useUpdateCatalog, useCheckCatalogCode } from '@/hooks/queries/useCatalogs'
import { useDebounce } from '@/hooks/useDebounce'
import type { Catalog, CreateCatalogRequest, UpdateCatalogRequest } from '@/types/catalog'


interface CatalogFormDialogProps {
  isOpen: boolean
  onClose: () => void
  catalog?: Catalog | null
}

export function CatalogFormDialog({ isOpen, onClose, catalog }: CatalogFormDialogProps) {
  const isEdit = !!catalog
  const createMutation = useCreateCatalog()
  const updateMutation = useUpdateCatalog()
  
  const [formData, setFormData] = useState({
    catalogCode: '',
    catalogName: '',
    description: '',
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE'
  })
  
  const [codeError, setCodeError] = useState('')
  const [nameError, setNameError] = useState('')
  const [checkingCode, setCheckingCode] = useState(false)
  
  const debouncedCode = useDebounce(formData.catalogCode, 500)
  const { data: codeCheckResult, isLoading: isCheckingCode } = useCheckCatalogCode(
    debouncedCode,
    !isEdit && debouncedCode.length > 0 && /^[A-Z0-9_]+$/.test(debouncedCode)
  )

  useEffect(() => {
    if (catalog) {
      setFormData({
        catalogCode: catalog.catalogCode,
        catalogName: catalog.catalogName,
        description: catalog.description || '',
        status: catalog.status
      })
    } else {
      setFormData({
        catalogCode: '',
        catalogName: '',
        description: '',
        status: 'ACTIVE'
      })
    }
    setCodeError('')
    setNameError('')
  }, [catalog, isOpen])

  useEffect(() => {
    if (!isEdit && codeCheckResult) {
      if (codeCheckResult.exists) {
        setCodeError('Mã danh mục đã tồn tại')
      } else {
        setCodeError('')
      }
    }
  }, [codeCheckResult, isEdit])

  const validateForm = () => {
    let isValid = true
    
    // Validate catalog code
    if (!isEdit) {
      if (!formData.catalogCode) {
        setCodeError('Mã danh mục là bắt buộc')
        isValid = false
      } else if (!/^[A-Z0-9_]+$/.test(formData.catalogCode)) {
        setCodeError('Mã chỉ chứa chữ in hoa, số và dấu gạch dưới')
        isValid = false
      } else if (formData.catalogCode.length > 50) {
        setCodeError('Mã danh mục không được quá 50 ký tự')
        isValid = false
      } else if (codeCheckResult?.exists) {
        setCodeError('Mã danh mục đã tồn tại')
        isValid = false
      }
    }
    
    // Validate catalog name
    if (!formData.catalogName) {
      setNameError('Tên danh mục là bắt buộc')
      isValid = false
    } else if (formData.catalogName.length > 100) {
      setNameError('Tên danh mục không được quá 100 ký tự')
      isValid = false
    } else {
      setNameError('')
    }
    
    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      if (isEdit) {
        const updateData: UpdateCatalogRequest = {
          catalogName: formData.catalogName,
          description: formData.description || undefined,
          status: formData.status
        }
        await updateMutation.mutateAsync({ id: catalog.id, data: updateData })
      } else {
        const createData: CreateCatalogRequest = {
          catalogCode: formData.catalogCode.toUpperCase(),
          catalogName: formData.catalogName,
          description: formData.description || undefined
        }
        await createMutation.mutateAsync(createData)
      }
      onClose()
    } catch (error) {
      // Error is handled by mutation hooks
    }
  }

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_]/g, '')
    setFormData({ ...formData, catalogCode: value })
    setCodeError('')
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới'}
      className="sm:max-w-md"
    >
      <form onSubmit={handleSubmit} className="p-5">
        <div className="space-y-4">
          {/* Catalog Code */}
          <div>
            <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1.5">
              Mã danh mục <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.catalogCode}
                onChange={handleCodeChange}
                disabled={isEdit}
                placeholder="VD: MEDICAL_DEVICE"
                className={`w-full h-10 px-3 py-2 border-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  isEdit 
                    ? 'bg-gray-50 dark:bg-gray-700 cursor-not-allowed text-gray-500 dark:text-gray-400'
                    : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                } ${
                  codeError 
                    ? 'border-red-400 focus:border-red-500 focus:ring-4 focus:ring-red-100 dark:focus:ring-red-900/20' 
                    : 'border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900/20'
                } focus:outline-none`}
              />
              {!isEdit && formData.catalogCode && (
                <div className="absolute right-2.5 top-1/2 -translate-y-1/2">
                  {isCheckingCode ? (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  ) : codeCheckResult?.exists ? (
                    <div className="w-4 h-4 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                      <span className="text-red-600 dark:text-red-400 text-xs font-bold">✕</span>
                    </div>
                  ) : formData.catalogCode && /^[A-Z0-9_]+$/.test(formData.catalogCode) ? (
                    <div className="w-4 h-4 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <span className="text-green-600 dark:text-green-400 text-xs font-bold">✓</span>
                    </div>
                  ) : null}
                </div>
              )}
            </div>
            {codeError && (
              <p className="mt-1.5 text-sm text-red-600 dark:text-red-400 font-medium">
                {codeError}
              </p>
            )}
            {!isEdit && !codeError && (
              <p className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
                Chỉ chứa chữ in hoa, số và dấu gạch dưới
              </p>
            )}
          </div>

          {/* Catalog Name */}
          <div>
            <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1.5">
              Tên danh mục <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.catalogName}
              onChange={(e) => {
                setFormData({ ...formData, catalogName: e.target.value })
                setNameError('')
              }}
              placeholder="VD: Thiết bị y tế"
              className={`w-full h-10 px-3 py-2 border-2 rounded-lg transition-all duration-200 text-sm font-medium bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${
                nameError 
                  ? 'border-red-400 focus:border-red-500 focus:ring-4 focus:ring-red-100 dark:focus:ring-red-900/20' 
                  : 'border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900/20'
              } focus:outline-none`}
            />
            {nameError && (
              <p className="mt-1.5 text-sm text-red-600 dark:text-red-400 font-medium">
                {nameError}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1.5">
              Mô tả
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              placeholder="Nhập mô tả cho danh mục..."
              className="w-full px-3 py-2 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900/20 focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-200 text-sm resize-none"
            />
          </div>

          {/* Status (only for edit) */}
          {isEdit && (
            <div>
              <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1.5">
                Trạng thái
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as 'ACTIVE' | 'INACTIVE' })}
                className="w-full h-10 px-3 py-2 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900/20 focus:outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-200 text-sm font-medium"
              >
                <option value="ACTIVE">Hoạt động</option>
                <option value="INACTIVE">Không hoạt động</option>
              </select>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-3 mt-6 pt-4 border-t border-gray-100 dark:border-gray-700">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={createMutation.isPending || updateMutation.isPending}
            className="flex-1"
          >
            Hủy
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={
              createMutation.isPending || 
              updateMutation.isPending ||
              (!isEdit && (isCheckingCode || codeCheckResult?.exists))
            }
            className="flex-1"
          >
            {createMutation.isPending || updateMutation.isPending ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Đang xử lý...
              </>
            ) : isEdit ? (
              'Cập nhật'
            ) : (
              'Tạo mới'
            )}
          </Button>
        </div>
      </form>
    </Dialog>
  )
}