'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Plus, Download, Upload } from 'lucide-react'
import { MainLayout } from '@/components/layout/MainLayout'
import { CatalogTable } from '@/components/catalogs/CatalogTable'
import { CatalogFilters } from '@/components/catalogs/CatalogFilters'
import { CatalogFormDialog } from '@/components/catalogs/CatalogFormDialog'
import { DeleteCatalogDialog } from '@/components/catalogs/DeleteCatalogDialog'
import { Pagination } from '@/components/ui/Pagination'
import { Button } from '@/components/ui/Button'
import { useCatalogs, useExportCatalogs, useCatalog } from '@/hooks/queries/useCatalogs'
import { useAuth } from '@/contexts/AuthContext'
import type { Catalog, CatalogFilters as CatalogFiltersType } from '@/types/catalog'

export default function CatalogsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const isAdmin = user?.role === 'ADMIN'
  
  // Check if we need to open edit dialog from URL params
  const editId = searchParams.get('edit')
  
  const [filters, setFilters] = useState<CatalogFiltersType>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const [isFormOpen, setIsFormOpen] = useState(false)
  const [isDeleteOpen, setIsDeleteOpen] = useState(false)
  const [selectedCatalog, setSelectedCatalog] = useState<Catalog | null>(null)

  const { data, isLoading } = useCatalogs(filters)
  const exportMutation = useExportCatalogs()
  
  // Load catalog for editing if editId is provided
  const { data: editCatalog } = useCatalog(editId || '', !!editId)
  
  useEffect(() => {
    if (editId && editCatalog) {
      setSelectedCatalog(editCatalog)
      setIsFormOpen(true)
      // Remove edit param from URL
      const newParams = new URLSearchParams(searchParams)
      newParams.delete('edit')
      router.replace(`/catalogs${newParams.toString() ? `?${newParams.toString()}` : ''}`)
    }
  }, [editId, editCatalog, router, searchParams])

  const handleFilterChange = (newFilters: CatalogFiltersType) => {
    setFilters(newFilters)
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handleSort = (column: 'catalogCode' | 'catalogName' | 'createdAt') => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
      page: 1
    }))
  }

  const handleCreate = () => {
    setSelectedCatalog(null)
    setIsFormOpen(true)
  }

  const handleEdit = (catalog: Catalog) => {
    setSelectedCatalog(catalog)
    setIsFormOpen(true)
  }

  const handleDelete = (catalog: Catalog) => {
    setSelectedCatalog(catalog)
    setIsDeleteOpen(true)
  }

  const handleView = (catalog: Catalog) => {
    router.push(`/catalogs/${catalog.id}`)
  }

  const handleExport = () => {
    exportMutation.mutate(filters)
  }

  const handleImport = () => {
    // TODO: Open import dialog
    console.log('Import catalogs')
  }

  return (
    <MainLayout>
      <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                Quản lý danh mục
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Quản lý danh mục thiết bị trong hệ thống
              </p>
            </div>
            <div className="flex items-center gap-3">
              {isAdmin && (
                <>
                  <Button
                    variant="secondary"
                    onClick={handleImport}
                    className="flex items-center gap-2"
                  >
                    <Upload className="w-5 h-5" />
                    Nhập
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={handleExport}
                    disabled={exportMutation.isPending}
                    className="flex items-center gap-2"
                  >
                    <Download className="w-5 h-5" />
                    Xuất
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleCreate}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-5 h-5" />
                    Thêm danh mục
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <CatalogFilters 
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        {/* Table */}
        <CatalogTable
          catalogs={data?.catalogs || []}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onView={handleView}
          isLoading={isLoading}
          sortBy={filters.sortBy}
          sortOrder={filters.sortOrder}
          onSort={handleSort}
        />

        {/* Pagination */}
        {data && (
          <div className="mt-6">
            <Pagination
              currentPage={data.page}
              totalPages={data.totalPages}
              totalRecords={data.total}
              pageSize={filters.limit || 10}
              onPageChange={handlePageChange}
              onPageSizeChange={(size) => setFilters(prev => ({ ...prev, limit: size, page: 1 }))}
            />
          </div>
        )}

        {/* Dialogs */}
        <CatalogFormDialog
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false)
            setSelectedCatalog(null)
          }}
          catalog={selectedCatalog}
        />

        <DeleteCatalogDialog
          isOpen={isDeleteOpen}
          onClose={() => {
            setIsDeleteOpen(false)
            setSelectedCatalog(null)
          }}
          catalog={selectedCatalog}
        />
      </div>
    </MainLayout>
  )
}