import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'
import { BiddingDocumentStatus } from '@prisma/client'

const createBiddingDocumentSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  customerName: z.string().optional(),
  status: z.nativeEnum(BiddingDocumentStatus).optional().default(BiddingDocumentStatus.PENDING),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') as BiddingDocumentStatus | null
    
    const skip = (page - 1) * limit

    const where: any = {}
    
    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (status) {
      where.status = status
    }

    const [data, total] = await Promise.all([
      prisma.biddingDocument.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
        },
      }),
      prisma.biddingDocument.count({ where }),
    ])

    return NextResponse.json({
      items: data,
      total,
      page,
      limit,
    })
  } catch (error) {
    console.error('Error fetching bidding documents:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bidding documents' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createBiddingDocumentSchema.parse(body)

    const existingDocument = await prisma.biddingDocument.findUnique({
      where: { code: validatedData.code },
    })

    if (existingDocument) {
      return NextResponse.json(
        { error: 'Bidding document with this code already exists' },
        { status: 400 }
      )
    }

    const biddingDocument = await prisma.biddingDocument.create({
      data: {
        ...validatedData,
        createdBy: session.userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    })

    return NextResponse.json(biddingDocument, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }
    console.error('Error creating bidding document:', error)
    return NextResponse.json(
      { error: 'Failed to create bidding document' },
      { status: 500 }
    )
  }
}