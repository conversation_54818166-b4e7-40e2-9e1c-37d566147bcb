'use client'

import { useState } from 'react'
import { FileText, Download, Trash2, Edit3, Cloud, Server } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Dialog } from '@/components/ui/Dialog'
import { useToast } from '@/hooks/useToast'
import { formatBytes } from '@/utils/format'
import { GoogleDriveViewer } from '@/components/equipment/GoogleDriveViewer'
import type { BiddingDocumentAttachment } from '@/types/biddingDocument'

interface BiddingDocumentListProps {
  attachments: BiddingDocumentAttachment[]
  onDelete: (attachmentId: string) => void
  canDelete?: boolean
  canView?: boolean
}

export function BiddingDocumentList({ 
  attachments,
  onDelete,
  canDelete = true,
  canView = true
}: BiddingDocumentListProps) {
  const toast = useToast()
  const [selectedDocument, setSelectedDocument] = useState<BiddingDocumentAttachment | null>(null)
  const [showVie<PERSON>, setShowViewer] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null)

  const handleDelete = async () => {
    if (!documentToDelete) return
    
    try {
      onDelete(documentToDelete)
      setShowDeleteDialog(false)
      setDocumentToDelete(null)
      toast.success('Đã xóa tài liệu thành công')
    } catch (error) {
      console.error('Error deleting document:', error)
      toast.error('Lỗi khi xóa tài liệu')
    }
  }

  const handleDownload = async (attachment: BiddingDocumentAttachment) => {
    try {
      if (attachment.source === 'google_drive' && attachment.googleDriveId) {
        // Open Google Drive file in new tab
        window.open(attachment.googleDriveUrl, '_blank')
      } else {
        // Download from server
        const response = await fetch(attachment.fileUrl)
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = attachment.fileName
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Lỗi khi tải xuống tài liệu')
    }
  }

  const handleViewEdit = (attachment: BiddingDocumentAttachment) => {
    if (attachment.source === 'google_drive') {
      // For Google Drive files, open in GoogleDriveViewer with edit mode
      const editDoc = { ...attachment, _viewMode: 'edit' } as any;
      setSelectedDocument(editDoc);
      setShowViewer(true);
    } else {
      // For server files (PDFs), open in new tab for viewing
      window.open(attachment.fileUrl, '_blank');
    }
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType === 'application/pdf') {
      return <FileText className="w-5 h-5 text-red-500" />
    }
    if (mimeType?.includes('word') || mimeType?.includes('document')) {
      return <FileText className="w-5 h-5 text-blue-500" />
    }
    return <FileText className="w-5 h-5 text-gray-500" />
  }

  if (!attachments || attachments.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p>Chưa có tài liệu nào được tải lên</p>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-3">
        {attachments.map((attachment) => (
          <div
            key={attachment.id}
            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center space-x-3 flex-1">
              {getFileIcon(attachment.mimeType)}
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {attachment.fileName}
                  </p>
                  {attachment.source === 'google_drive' ? (
                    <span title="Google Drive">
                      <Cloud className="w-4 h-4 text-blue-500" />
                    </span>
                  ) : (
                    <span title="Server">
                      <Server className="w-4 h-4 text-gray-500" />
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatBytes(attachment.fileSize)} • {new Date(attachment.uploadedAt).toLocaleString('vi-VN')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {canView && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewEdit(attachment)}
                  className="flex items-center gap-2"
                  title={attachment.source === 'google_drive' ? "Xem/Chỉnh sửa trong Google Drive" : "Xem tài liệu"}
                >
                  <Edit3 className="w-4 h-4" />
                  Xem/Chỉnh sửa
                </Button>
              )}
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleDownload(attachment)}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Tải xuống
              </Button>
              
              {canDelete && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setDocumentToDelete(attachment.id)
                    setShowDeleteDialog(true)
                  }}
                  className="flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                >
                  <Trash2 className="w-4 h-4" />
                  Xóa
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Google Drive Viewer Modal */}
      {showViewer && selectedDocument  && (
        <GoogleDriveViewer
          key={selectedDocument.id}
          file={{
            id: selectedDocument.googleDriveId || selectedDocument.id,
            name: selectedDocument.fileName,
            mimeType: selectedDocument.mimeType || 'application/octet-stream',
            url: selectedDocument.googleDriveUrl || selectedDocument.fileUrl
          }}
          onClose={() => {
            setShowViewer(false);
            setSelectedDocument(null);
          }}
          allowEdit={selectedDocument?._viewMode === 'edit'}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setDocumentToDelete(null)
        }}
        title="Xác nhận xóa"
      >
        <div className="space-y-4">
          <p>Bạn có chắc chắn muốn xóa tài liệu này?</p>
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowDeleteDialog(false)
                setDocumentToDelete(null)
              }}
            >
              Hủy
            </Button>
            <Button
              type="button"
              variant="primary"
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xóa
            </Button>
          </div>
        </div>
      </Dialog>
    </>
  )
}