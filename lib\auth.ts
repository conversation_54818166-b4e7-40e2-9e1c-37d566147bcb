import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'
import { z } from 'zod'
import type { User, Role } from '@prisma/client'

// Validation schemas
export const loginSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')

// JWT token types
export interface TokenPayload {
  userId: string
  username: string
  role: Role
  sessionId?: string
}

export interface RefreshTokenPayload {
  userId: string
  sessionId: string
}

// Password hashing
export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(12)
  return bcrypt.hash(password, salt)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// JWT token generation and verification
const jwtSecret = new TextEncoder().encode(process.env.JWT_SECRET!)
const refreshSecret = new TextEncoder().encode(process.env.JWT_REFRESH_SECRET!)

export async function generateAccessToken(payload: TokenPayload): Promise<string> {
  const duration = process.env.ACCESS_TOKEN_DURATION || '10m'
  
  return new SignJWT({ ...payload } as any)
    .setProtectedHeader({ alg: 'HS256' })
    .setJti(crypto.randomUUID())
    .setIssuedAt()
    .setExpirationTime(duration)
    .sign(jwtSecret)
}

export async function generateRefreshToken(payload: RefreshTokenPayload): Promise<string> {
  const duration = process.env.REFRESH_DURATION || '7d'
  
  return new SignJWT({ ...payload } as any)
    .setProtectedHeader({ alg: 'HS256' })
    .setJti(crypto.randomUUID())
    .setIssuedAt()
    .setExpirationTime(duration)
    .sign(refreshSecret)
}

export async function verifyAccessToken(token: string): Promise<TokenPayload | null> {
  try {
    const { payload } = await jwtVerify(token, jwtSecret)
    return payload as unknown as TokenPayload
  } catch (error) {
    console.error('Access token verification failed:', error)
    return null
  }
}

// Alias for verifyAccessToken for backward compatibility
export const verifyToken = verifyAccessToken

export async function verifyRefreshToken(token: string): Promise<RefreshTokenPayload | null> {
  try {
    const { payload } = await jwtVerify(token, refreshSecret)
    return payload as unknown as RefreshTokenPayload
  } catch (error) {
    console.error('Refresh token verification failed:', error)
    return null
  }
}

// Session management helpers
export function generateSessionToken(): string {
  return crypto.randomUUID()
}

// Rate limiting helpers
export function parseDuration(duration: string): number {
  const match = duration.match(/^(\d+)([mhd])$/)
  if (!match) return 15 * 60 * 1000 // Default 15 minutes
  
  const [, value, unit] = match
  const num = parseInt(value)
  
  switch (unit) {
    case 'm': return num * 60 * 1000
    case 'h': return num * 60 * 60 * 1000
    case 'd': return num * 24 * 60 * 60 * 1000
    default: return 15 * 60 * 1000
  }
}

export function getExpirationDate(duration: string): Date {
  return new Date(Date.now() + parseDuration(duration))
}

// Security headers for cookies
export const AUTH_COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  path: '/',
  maxAge: parseDuration(process.env.SESSION_DURATION || '15m') / 1000,
}

export const REFRESH_COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  path: '/',
  maxAge: parseDuration(process.env.REFRESH_DURATION || '7d') / 1000,
}

// IP Address extraction
export function getClientIp(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0].trim() : 
    request.headers.get('x-real-ip') || 
    'unknown'
  return ip
}

// User agent extraction
export function getUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'unknown'
}

// Audit log action types
export const AuditAction = {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILED: 'LOGIN_FAILED',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',
  PASSWORD_RESET: 'PASSWORD_RESET',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED: 'ACCOUNT_UNLOCKED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  TOKEN_REFRESHED: 'TOKEN_REFRESHED',
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  VIEW: 'VIEW',
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  USER_CREATED: 'USER_CREATED',
  USER_UPDATED: 'USER_UPDATED',
  USER_DELETED: 'USER_DELETED',
} as const

export type AuditActionType = typeof AuditAction[keyof typeof AuditAction]